"""
Django settings for devproject project.

Generated by 'django-admin startproject' using Django 5.0.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
from datetime import timedelta
import os 
from decouple import config
from celery.schedules import crontab

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-@2+&i53i^-4la((t&k*!d@6us&+wb9y#b!z83dn@0kop&oq_2%'

# SECURITY WARNING: don't run with debug turned on in production!
# # DEBUG = True
# if os.environ['DEBUG'] == "True":
#     DEBUG = True
# else:
#     DEBUG = False
    
# DEBUG = True
DEBUG = config('DEBUG_MODE', default=False, cast=bool)
print(f"settings.py's DEBUG_MODE - {DEBUG}")
print(f"settings.py's DEBUG_MODE - {type(DEBUG)}")


# ALLOWED_HOSTS = [ 'argt-linebot.tokyo.cs.ait.ac.th', 'localhost', 

# TODO - open CSRF_TRUSTED_ORIGINS, CORS_ALLOWED_ORIGINS for staging and production
# ALLOWED_HOSTS = ["127.0.0.1", "localhost"]

# TODO - Delete this
# Add public GitHub URL for testing websocket in Local

# ALLOWED_HOSTS = ['*'] # For development only. Configure properly for production








ALLOWED_HOSTS = ["127.0.0.1", "localhost", '*.app.github.dev']
# if("ALLOWED_HOSTS" not in os.environ.keys()):
#     print("There is no 'ALLOWED_HOSTS' in the environment variable")
# else:
#     # ALLOWED_HOSTS = ["127.0.0.1", "localhost", '.app.github.dev']
#     ALLOWED_HOSTS += os.environ["ALLOWED_HOSTS"].split(',')
#     print(f"settings.py's ALLOWED_HOSTS - {ALLOWED_HOSTS}")

#     # CSRF_TRUSTED_ORIGINS = ALLOWED_HOSTS
#     # CORS_ALLOWED_ORIGINS = ALLOWED_HOSTS
#     CSRF_TRUSTED_ORIGINS = ['https://'+url for url in os.environ["ALLOWED_HOSTS"].split(',')]
#     print(f"settings.py's CSRF_TRUSTED_ORIGINS - {CSRF_TRUSTED_ORIGINS}")

#     CORS_ALLOWED_ORIGINS = ['https://'+url for url in os.environ["ALLOWED_HOSTS"].split(',')]
#     print(f"settings.py's CORS_ALLOWED_ORIGINS - {CORS_ALLOWED_ORIGINS}")

# CORS_ALLOW_ALL_ORIGINS = True

# # Or specify allowed domains
# CORS_ALLOWED_ORIGINS = [
#     "http://127.0.0.1",
#     "http://localhost",
#     # "http://localhost:8000",
#     "https://www.salmate-staging.aibrainlab.co",
#     "https://www.backend.salmate-staging.aibrainlab.co",
#     "https://information.llm.salmate-staging.aibrainlab.co",
#     "https://salmatestorage.blob.core.windows.net"
# ]

# # For allowing credentials (cookies, authorization headers)
# CORS_ALLOW_CREDENTIALS = True

# # For GitHub Codespaces or similar environments
# CSRF_TRUSTED_ORIGINS = [
#     "http://127.0.0.1",
#     "http://localhost",
#     # "http://localhost:8000",
#     "https://*.app.github.dev"
#     "https://www.salmate-staging.aibrainlab.co",
#     "https://www.backend.salmate-staging.aibrainlab.co", 
#     "https://information.llm.salmate-staging.aibrainlab.co",
# ]











PUBLIC_BACKEND_URL = os.environ.get("PUBLIC_BACKEND_URL")
ALLOWED_HOSTS = ["127.0.0.1", "localhost", '*.app.github.dev', "localhost:8001"]
CORS_ALLOWED_ORIGINS = ["http://127.0.0.1", "http://localhost", "http://localhost:8001", "https://*.app.github.dev"]
CSRF_TRUSTED_ORIGINS = ["http://127.0.0.1", "http://localhost", "http://localhost:8001", "https://*.app.github.dev"]

if("ALLOWED_HOSTS" not in os.environ.keys()):
    print("There is no 'ALLOWED_HOSTS' in the environment variable")
else:
    ALLOWED_HOSTS += os.environ["ALLOWED_HOSTS"].split(',')

    CORS_ALLOWED_ORIGINS += ['https://'+url for url in os.environ["ALLOWED_HOSTS"].split(',')]
    CSRF_TRUSTED_ORIGINS += ['https://'+url for url in os.environ["ALLOWED_HOSTS"].split(',')]

    # CORS_ALLOWED_ORIGINS = [url for url in os.environ["ALLOWED_HOSTS"].split(',')]
    # CSRF_TRUSTED_ORIGINS = [url for url in os.environ["ALLOWED_HOSTS"].split(',')]

# ALLOW_ORIGINS='*'
# CORS_ALLOWED_ORIGINS = '*'
# CSRF_TRUSTED_ORIGINS = '*'

# Allow all domains (for development only)
CORS_ALLOW_ALL_ORIGINS = True

# For allowing credentials (cookies, authorization headers)
CORS_ALLOW_CREDENTIALS = True

print(f"settings.py's ALLOWED_HOSTS - {ALLOWED_HOSTS}")
print(f"settings.py's CSRF_TRUSTED_ORIGINS - {CSRF_TRUSTED_ORIGINS}")
print(f"settings.py's CORS_ALLOWED_ORIGINS - {CORS_ALLOWED_ORIGINS}")


# Application definition
INSTALLED_APPS = [
    # "user.apps.UserConfig",
    "daphne",
    "channels_redis",
    # "channels",
    "user",
    "dashboard",
    "setting.apps.SettingsConfig",
    "llm_rag_doc.apps.LlmRagDocConfig",
    "linechatbot.apps.LinechatbotConfig",
    # 'connectors.apps.ConnectorsConfig',
    # "ticket.apps.TicketConfig",
    "customer.apps.CustomerConfig",
    'connectors',
    'consent',
    'export',
    'third_party_providers',
    'subscription.apps.SubscriptionConfig',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'ticket',
    'rest_framework',
    'rest_framework.authtoken',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    # 'rest_framework_csv', # For Dashboard CSV export
    'drf_excel', # For Dashboard XLSX export
    'django_filters',
    'drf_yasg',
    'corsheaders',
    'pgvector.django',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    # 'connectors.middleware.LoggingMiddleware',
    # 'connectors.middleware.ErrorHandlingMiddleware',

    'user.middleware.activity_tracking.ActivityTrackingMiddleware', # Tracking user activities

]

ROOT_URLCONF = 'devproject.urls'

# Path for uploaded file
MEDIA_URL = '/uploaded_files/'  # URL to access uploaded files
MEDIA_ROOT = os.path.join(BASE_DIR, 'uploaded_files')  # Directory where uploaded files are stored
# STATIC_URL = '/uploaded_files/'
# STATIC_ROOT = os.path.join(BASE_DIR, 'uploaded_files')

REFRESH_TOKEN_LIFETIME_DAYS = 6
SIMPLE_JWT = {
	# unit options - minutes, hours, days
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=12),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=REFRESH_TOKEN_LIFETIME_DAYS), # Update AWAY_TO_OFFLINE too
    # "ACCESS_TOKEN_LIFETIME": timedelta(minutes=1),
    # "REFRESH_TOKEN_LIFETIME": timedelta(minutes=5),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY, # settings.SECRET_KEY, SECRET_KEY
    "AUTH_HEADER_TYPES": ("Bearer",), # "Bearer <Acess Token>"
}
print(f"REFRESH_TOKEN_LIFETIME_DAYS's values - {REFRESH_TOKEN_LIFETIME_DAYS}")
# print(f"SIMPLE_JWT's values - {SIMPLE_JWT}")

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        # 'DIRS': [ BASE_DIR / 'templates' ], # TODO - Close this after websocket with Frontend
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI_APPLICATION = 'devproject.wsgi.application'
ASGI_APPLICATION = 'devproject.asgi.application'

# Channel layer settings
# # CHANNEL_LAYERS for dev environment
# CHANNEL_LAYERS = {
#     "default": {
#         "BACKEND": "channels.layers.InMemoryChannelLayer"
#     }
# }

REDIS_HOST = os.environ.get('REDIS_HOST', 'redis')
REDIS_PORT = os.environ.get('REDIS_PORT', 6379)
REDIS_IMAGE_BUFFER_DB = 2  # Redis Database for image buffering (LINE app)

# Image buffer settings
IMAGE_BUFFER_TIMEOUT_MINUTES = 3
IMAGE_BUFFER_TTL_SECONDS = 300  # 5 minutes
MAX_IMAGES_PER_SET = 300

# CHANNEL_LAYERS = {
#     'default': {
#         'BACKEND': 'channels_redis.core.RedisChannelLayer',
#         'CONFIG': {
#             "hosts": [(REDIS_HOST, int(REDIS_PORT))],
#             # "hosts": [('redis', 6379)],
#             # "hosts": [('127.0.0.1', 6379)],
#             # "hosts": ['redis://redis:6379/1']
#             # "hosts": [('redis.salmate-staging.aibrainlab.co', 6379)],
            
#             "capacity": 1500,  # Optional: adjust channel capacity
#             "expiry": 3600,   # Optional: message expiry in seconds
#         },
#     },
# }

# CHANNEL_LAYERS = {
#     'default': {
#         'BACKEND': 'channels_redis.core.RedisChannelLayer',
#         'CONFIG': {
#             "hosts": [(REDIS_HOST, int(REDIS_PORT))],
#             # "hosts": [('redis', 6379)],
#             # "hosts": [('127.0.0.1', 6379)],
#             # "hosts": ['redis://redis:6379/1']
#             # "hosts": [('redis.salmate-staging.aibrainlab.co', 6379)],
            
#             "capacity": 10000,  # Optional: adjust channel capacity
#             # "capacity": 1500,  # Optional: adjust channel capacity
#             "expiry": 3600,   # Optional: message expiry in seconds
#             "group_expiry": 86400
#         },
#     },
# }

# Redis Configuration for Activity Tracking
# REDIS_HOST = 'localhost'
# REDIS_PORT = 6379
REDIS_ACTIVITY_DB = 3  # Redis Database use as a separate DB for activity tracking

# Activity Tracking Settings
ACTIVITY_TRACKING_EXCLUDED_PATHS = [
    '/static/',
    '/media/',
    '/health/',
    '/metrics/',
    'user/api/user/activity/',  # Prevent recursive tracking
    '/favicon.ico',
    '/robots.txt',
]

ACTIVITY_TRACKING_NON_RESET_PATHS = [
    '/user/api/user/status/',
    '/api/notifications/check/',
    '/api/websocket/ping/',
    '/user/api/users/paginated/',
]

# Auto Status Management Settings (These will be managed via SettingsService)
# Default values shown here for reference
INACTIVITY_THRESHOLDS = {
    'ONLINE_TO_AWAY': os.environ.get("INACTIVITY_THRESHOLD_ONLINE_TO_AWAY", 15), # minutes
    # 'AWAY_TO_OFFLINE': int(60*24*int(REFRESH_TOKEN_LIFETIME_DAYS)),     # minutes, depend on REFRESH_TOKEN_LIFETIME too
    'ACTIVITY_POLL_INTERVAL': 30,  # seconds
    'STATUS_CHECK_INTERVAL': 60,   # seconds
}

print(f"INACTIVITY_THRESHOLDS's values - {INACTIVITY_THRESHOLDS}")

# Auto Return to Online Settings
AUTO_RETURN_TO_ONLINE_ENABLED = True
AUTO_RETURN_TO_ONLINE_MIN_WEIGHT = 50 # Other api-level
# AUTO_RETURN_TO_ONLINE_MIN_WEIGHT = 10 # Other api-level


# # System Settings to be added via SettingsService
# # These should be created in a migration or management command
# SYSTEM_SETTINGS_DEFAULTS = {
#     'INACTIVITY_THRESHOLD_ONLINE_TO_AWAY': {
#         'value': '5',
#         'value_type': 'int',
#         'description': 'Minutes of inactivity before user status changes from ONLINE to AWAY',
#         'category': 'activity_tracking'
#     },
#     'INACTIVITY_THRESHOLD_AWAY_TO_OFFLINE': {
#         'value': '15',
#         'value_type': 'int',
#         'description': 'Minutes of inactivity before user status changes from AWAY to OFFLINE',
#         'category': 'activity_tracking'
#     },
#     'ACTIVITY_POLL_INTERVAL': {
#         'value': '30',
#         'value_type': 'int',
#         'description': 'Seconds between frontend activity polls',
#         'category': 'activity_tracking'
#     },
#     'STATUS_CHECK_INTERVAL': {
#         'value': '60',
#         'value_type': 'int',
#         'description': 'Seconds between backend status checks',
#         'category': 'activity_tracking'
#     },
#     'AUTO_RETURN_TO_ONLINE_ENABLED': {
#         'value': 'True',
#         'value_type': 'bool',
#         'description': 'Enable automatic return to ONLINE status on activity',
#         'category': 'activity_tracking'
#     },
#     'AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION': {
#         'value': 'True',
#         'value_type': 'bool',
#         'description': 'Consider work schedules when transferring tickets',
#         'category': 'ticket_transfer'
#     }
# }


CHANNEL_LAYERS = {
    'default': {  # The default channel layer configuration
        'BACKEND': 'channels_redis.core.RedisChannelLayer',  # Using Redis as the backend
        'CONFIG': {  # Configuration options
            # "hosts": [(REDIS_HOST, int(REDIS_PORT))],  # Redis server location
            "hosts": [f"redis://{REDIS_HOST}:{REDIS_PORT}/0"], # Redis server location
            "capacity": 10000,  # Total message capacity of the channel layer
            "expiry": 1800,  # How long (in seconds) a message is kept before expiring
            "channel_capacity": {  # Per-channel capacity limits
                # Default channel capacity
                "http.request": 1000,  # HTTP request channel can hold 1000 messages
                # Per-channel overrides
                "chat.*": 2000,  # All chat channels can hold 2000 messages
            },
        },
    },
}

# WebSocket URL configuration (must matching with the one in routing.py file)
# WEBSOCKET_URL = '/ws/chat/'
# WEBSOCKET_URL = '/ws/customer/'
# WEBSOCKET_URL = '/ws/customers/'
WEBSOCKET_URL = '/ws/platforms/'

# # Add these to help manage WebSocket connections
# CHAT_SOCKET_TIMEOUT = 60  # seconds
# MAX_CONN_AGE = 600  # seconds

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

# TODO - Delete this
# # For Development environment ONLY
# tem_DB_HOST = os.environ.get('DB_HOST', 'postgres')
# tem_DB_NAME = os.environ.get('DB_NAME', 'devproject')
# tem_DB_USER = os.environ.get('DB_USER', 'admin')
# tem_DB_PASS = os.environ.get('DB_PASSWORD', 'password')
# tem_DB_PORT = os.environ.get('DB_PORT', '5432')

# tem_DB_HOST = os.environ.get('DB_HOST')
# tem_DB_NAME = os.environ.get('DB_NAME')
# tem_DB_USER = os.environ.get('DB_USER')
# tem_DB_PASS = os.environ.get('DB_PASS')
# tem_DB_PORT = os.environ.get('DB_PORT')

# print(f"setting's HOST - {tem_DB_HOST} ")
# print(f"setting's DB_NAME - {tem_DB_NAME} ")
# print(f"setting's USER - {tem_DB_USER} ")
# print(f"setting's PASSWORD - {tem_DB_PASS} ")
# print(f"setting's PORT - {tem_DB_PORT} ")

# DATABASES = {
#     # For Development environment ONLY
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql',
#         'NAME': 'devproject',
#         'USER': 'admin',
#         'PASSWORD': 'password',
#         'HOST': 'postgres',
#         'PORT': '5432',
#     }
# }

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ["DB_NAME"],
        "USER": os.environ["DB_USER"],
        "PASSWORD": os.environ["DB_PASS"],
        "HOST": os.environ["DB_HOST"],
        "PORT": os.environ["DB_PORT"],
        # Connection pooling settings
        'CONN_MAX_AGE': int(os.environ.get('DB_CONN_MAX_AGE', 60)),  # 10 minutes (Default)
        'CONN_HEALTH_CHECKS': True,
        # 'OPTIONS': {
        #     'connect_timeout': 10,
        #     'options': '-c statement_timeout=30000',  # 30 seconds
        #     'keepalives': 1,
        #     'keepalives_idle': 30,
        #     'keepalives_interval': 10,
        #     'keepalives_count': 5,
        # },
        # 'ATOMIC_REQUESTS': False,  # Don't wrap every request in transaction
    },
    "pgvector": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("VECTOR_DB_NAME", "devproject"),
        "USER": os.environ.get("VECTOR_DB_USER", "admin"),
        "PASSWORD": os.environ.get("VECTOR_DB_PASS", "password"),
        "HOST": os.environ.get("VECTOR_DB_HOST", "pgvector"),
        "PORT": os.environ.get("VECTOR_DB_PORT", "5432"),
    }

    # "default": {
    #     "ENGINE": "django.db.backends.postgresql",
    #     "NAME": os.environ.get('DB_NAME', 'devproject'),
    #     "USER": os.environ.get('DB_USER', 'admin'),
    #     "PASSWORD": os.environ.get('DB_PASSWORD', 'password'),
    #     "HOST": os.environ.get('DB_HOST', 'postgres'),
    #     "PORT": os.environ.get('DB_PORT', '5432')
    # }
}

DATABASE_ROUTERS = ["llm_rag_doc.models.DatabaseRouter"]

# # Specify the default of User model
AUTH_USER_MODEL = "user.User"

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
]

LANGUAGE_CODE = 'en-us'
LANGUAGES = [
    ('en', 'English'),
    ('th', 'Thai'),
]

TIME_ZONE = 'Asia/Bangkok'

USE_I18N = True
USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

# TODO - Open this when know how to deal with serving files in production
STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# API Endpoints

# ANALYSIS_API_URL = os.environ.get("ANALYSIS_API_URL", "https://vectordb.llm.salmate-staging.aibrainlab.co/summary/invoke/")
ANALYSIS_API_URL = os.environ.get("ANALYSIS_API_URL")
VECTORDB_API_URL = os.environ.get("VECTORDB_API_URL")

# Upload size
DATA_UPLOAD_MAX_MEMORY_SIZE = int(2.5 * 1024 * 1024) # 2.5 MB
FILE_UPLOAD_MAX_MEMORY_SIZE = int(2.5 * 1024 * 1024) # 2.5 MB
IMAGE_UPLOAD_MAX_MEMORY_SIZE = int(2.5 * 1024 * 1024) # 2.5 MB


# AZURE BLOB
AZURE_ACCOUNT_NAME = os.environ.get('AZURE_ACCOUNT_NAME')
AZURE_ACCOUNT_KEY = os.environ.get('AZURE_ACCOUNT_KEY')
AZURE_CONTAINER = os.environ.get('AZURE_CONTAINER')

# LLM
# LANGGRAPH_BASE_URL = os.getenv("LLM_SALMATE_LANGGRAPH", "https://langgraph.llm.salmate-staging.aibrainlab.co")
LANGGRAPH_BASE_URL = os.getenv("LLM_SALMATE_LANGGRAPH")

# Consent-specific settings
CONSENT_RETENTION_YEARS = 5  # How long to retain consent records
CONSENT_EXPIRY_WARNING_DAYS = 30  # Days before expiry to warn customers
CONSENT_RE_CONSENT_GRACE_PERIOD_DAYS = 30  # Days given to provide re-consent
CONSENT_MAX_REMINDER_COUNT = 3  # Maximum number of reminders to send

# LINE Rich Menu Configuration
LINE_RICH_MENU_NOT_CONSENTED = os.getenv('LINE_RICH_MENU_NOT_CONSENTED')
LINE_RICH_MENU_CONSENTED = os.getenv('LINE_RICH_MENU_CONSENTED')
LINE_RICH_MENU_RETRY_ATTEMPTS = int(os.getenv('LINE_RICH_MENU_RETRY_ATTEMPTS', 3))
LINE_RICH_MENU_RETRY_DELAY = int(os.getenv('LINE_RICH_MENU_RETRY_DELAY', 2))

# LINE Quota API Configuration
LINE_QUOTA_CACHE_TIMEOUT = int(os.getenv('LINE_QUOTA_CACHE_TIMEOUT', 300))  # 5 minutes
LINE_QUOTA_REQUEST_TIMEOUT = int(os.getenv('LINE_QUOTA_REQUEST_TIMEOUT', 10))  # 10 seconds
LINE_QUOTA_MAX_RETRIES = int(os.getenv('LINE_QUOTA_MAX_RETRIES', 2))
LINE_QUOTA_RETRY_DELAY = int(os.getenv('LINE_QUOTA_RETRY_DELAY', 1))

# TODO - Delete this
print(f"settings.py's LINE_RICH_MENU_NOT_CONSENTED - {LINE_RICH_MENU_NOT_CONSENTED}")
print(f"settings.py's LINE_RICH_MENU_CONSENTED - {LINE_RICH_MENU_CONSENTED}")

# CSAT
THRESHOLD_N_INCOMING_MESSAGE_BEFORE_SENDING_CSAT = int(os.getenv('THRESHOLD_N_INCOMING_MESSAGE_BEFORE_SENDING_CSAT', 3))

# Celery settings
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://redis:6379/1')
CELERY_RESULT_BACKEND = CELERY_BROKER_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'

CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES = os.getenv('CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES', 1)
CELERY_CHECK_DATABACKUP_AT_HOUR = os.getenv('CELERY_CHECK_DATABACKUP_AT_HOUR', 2)

# TODO - Delete this
print(f"settings.py's CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES - {CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES}")
print(f"settings.py's CELERY_CHECK_DATABACKUP_AT_HOUR - {CELERY_CHECK_DATABACKUP_AT_HOUR}")

CELERY_BEAT_SCHEDULE = {
    'close-inactive-tickets': {
        # 'task': 'ticket.tasks.close_inactive_tickets',
        'task': 'ticket.tasks.inactive_tickets',
        'schedule': crontab(minute=f'*/{CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES}'),  # run every 1 minutes
        'options': {
            'queue': 'default',  # Explicitly set queue
            'expires': 60.0,  # Task expires if not run within 60 seconds
        }
    },
    # 'inactive-tickets-01-hour': {
    #     'task': 'ticket.tasks.inactive_tickets_01_hour',
    #     'schedule': crontab(minute='*/1'),  # run every 1 minutes
    # },
    'backup-data-daily-at-02': {
        'task': 'ticket.tasks.backup_postgres_database',
        'schedule': crontab(hour=f'{CELERY_CHECK_DATABACKUP_AT_HOUR}', minute=0),  # Run at 2 AM daily
        # 'schedule': crontab(minute=f'*/{CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES}'),  # run every 1 minutes
        'options': {
            'queue': 'default',  # Explicitly set queue
            'expires': 60.0,  # Task expires if not run within 60 seconds
        }
    },
    # Check and update user statuses every 60 seconds
    'check-user-statuses': {
        'task': 'user.check_and_update_user_statuses', # Copied of task name @shared_task
        'schedule': 60.0,  # every 60 seconds
        'options': {
            'expires': 30.0,  # Task expires if not run within 30 seconds
        }
    },
    
    # # Send inactivity warnings every 30 seconds
    # 'send-inactivity-warnings': {
    #     'task': 'user.send_inactivity_warnings',
    #     'schedule': 30.0,  # every 30 seconds
    #     'options': {
    #         'expires': 15.0,
    #     }
    # },
    
    # Sync Redis to database every 5 minutes
    'sync-redis-to-database': {
        'task': 'user.sync_redis_to_database',
        'schedule': 300.0,  # every 5 minutes
        'options': {
            'expires': 60.0,
        }
    },

    # # Tasks about Customer Consent (FUTURE FEATURE or NOT)

    # 'check-expiring-consents': {
    #     'task': 'consent.tasks.check_expiring_consents',
    #     'schedule': crontab(hour=2, minute=0),  # Run daily at 2 AM
    # },
    # 'process-expired-consents': {
    #     'task': 'consent.tasks.process_expired_consents',
    #     'schedule': crontab(hour=3, minute=0),  # Run daily at 3 AM
    # },

    # # Verify rich menu assignments daily at 4 AM
    # 'verify-line-rich-menus': {
    #     'task': 'connectors.tasks.verify_rich_menu_assignments',
    #     'schedule': crontab(hour=4, minute=0),
    # },
}

# ============================================
# THIRD PARTY PROVIDER SETTINGS (COMMON)
# ============================================

THIRD_PARTY_PROVIDER_CACHE_TIMEOUT = 86400  # 24 hours

# Provider configurations (same for all environments)
PROVIDER_SETTINGS = {
    'LINE': {
        'validate_webhooks': True,
        'retry_attempts': 3,
        'timeout': 30,
    },
    'WHATSAPP': {
        'validate_webhooks': True,
        'retry_attempts': 3,
        'timeout': 30,
        # 'tier': os.environ.get('WHATSAPP_TIER', 'standard'),
    },
    'EMAIL': {
        'max_retry_attempts': 5,
        'timeout': 60,
    },
}

# Rate limiting base configuration
RATE_LIMIT_SETTINGS = {
    'ENABLE_RATE_LIMITING': True,
    'RATE_LIMIT_CACHE_BACKEND': 'provider_limits',
    'INCLUDE_RATE_LIMIT_HEADERS': True,
    'RATE_LIMIT_LOG_VIOLATIONS': True,
    # 'ALERT_ON_VIOLATIONS': True,
}

# File validation (same for all environments)
FILE_VALIDATION_SETTINGS = {
    'MAX_FILE_SIZE': 20 * 1024 * 1024,  # 20MB
    'ALLOWED_IMAGE_TYPES': ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'ALLOWED_VIDEO_TYPES': ['mp4', 'avi', 'mov', 'wmv'],
    'ALLOWED_AUDIO_TYPES': ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
    'ALLOWED_DOCUMENT_TYPES': ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
    'VALIDATE_FILE_CONTENT': True,
}

# Message validation
MESSAGE_VALIDATION_SETTINGS = {
    'STRICT_MODE': True,
    'LOG_VALIDATION_FAILURES': True,
    'CACHE_VALIDATION_RESULTS': True,
    'VALIDATION_CACHE_TIMEOUT': 300,  # 5 minutes
}

# # Performance monitoring
# THIRD_PARTY_PROVIDER_MONITORING = {
#     'TRACK_CACHE_PERFORMANCE': True,
#     'ALERT_ON_CACHE_MISS_RATE': 0.1,  # Alert if >10% cache misses
#     'DATADOG_METRICS_ENABLED': True,
# }

# ============================================
# PRODUCTION CACHE CONFIGURATION
# ============================================
# Use Redis with high availability in production
TEM_DEFAULT_CACHING = 4 # edis Database for default caching (temporary, suitable default database is 0, but Websocket channel is using it currerntly)
REDIS_THIRD_PARTY_PROVIDERS_DB = 5  # Redis Database for caching 3rd-party providers' info (limitations, etc.)

CACHES = {
    # 'default': {
    #     # Your existing production cache config
    #     'BACKEND': 'django_redis.cache.RedisCache',
    #     'LOCATION': os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379/0'),
    #     'OPTIONS': {
    #         'CLIENT_CLASS': 'django_redis.client.DefaultClient',
    #     }
    # },
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{int(REDIS_PORT)}/{TEM_DEFAULT_CACHING}",
    },

    # PRODUCTION: Use Redis with Sentinel for high availability
    'provider_limits': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': f'redis://{REDIS_HOST}:{int(REDIS_PORT)}/{REDIS_THIRD_PARTY_PROVIDERS_DB}',
    }

    # # PRODUCTION: Use Redis with Sentinel for high availability
    # 'provider_limits': {
    #     'BACKEND': 'django_redis.cache.RedisCache',
    #     # 'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        
    #     # Option 1: Simple Redis (single server)
    #     # 'LOCATION': os.environ.get('REDIS_PROVIDER_URL', 'redis://127.0.0.1:6379/2'),
        
    #     # Option 2: Redis with replicas (recommended)
    #     'LOCATION': [
    #         os.environ.get('REDIS_MASTER', f'redis://redis-master:6379/{REDIS_THIRD_PARTY_PROVIDERS_DB}'),
    #         os.environ.get('REDIS_SLAVE_1', f'redis://redis-slave-1:6379/{REDIS_THIRD_PARTY_PROVIDERS_DB}'),
    #         os.environ.get('REDIS_SLAVE_2', f'redis://redis-slave-2:6379/{REDIS_THIRD_PARTY_PROVIDERS_DB}'),
    #     ],
    #     # WHY: Multiple Redis servers for failover
    #     # EFFECT: If master dies, slaves take over
        
    #     'OPTIONS': {
    #         'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            
    #         'CONNECTION_POOL_KWARGS': {
    #             'max_connections': 100,  # More connections for production
    #             # WHY: Handle more concurrent requests
    #         },
            
    #         'SOCKET_CONNECT_TIMEOUT': 5,
    #         'SOCKET_TIMEOUT': 5,
            
    #         'RETRY_ON_TIMEOUT': True,
    #         # WHY: Retry failed Redis operations
    #         # EFFECT: Better reliability
            
    #         'PARSER_CLASS': 'redis.connection.HiredisParser',
    #         # WHY: C-based parser, 10x faster than Python parser
    #         # REQUIREMENT: pip install hiredis
            
    #         'PICKLE_VERSION': 2,
            
    #         'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            
    #         'IGNORE_EXCEPTIONS': True,
    #     },
        
    #     'KEY_PREFIX': 'tpp-prod',
    #     'TIMEOUT': 86400,  # 24 hours
    # }
}

# SMTP
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.environ.get("EMAIL_HOST")
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_HOST_PASSWORD")
EMAIL_PORT = os.environ.get("EMAIL_PORT")
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False

# Connector settings
CONNECTOR_ALERT_EMAILS = ['<EMAIL>']
FACEBOOK_APP_ID = 'your-facebook-app-id'
FACEBOOK_APP_SECRET = 'your-facebook-app-secret'
FACEBOOK_REDIRECT_URI = 'https://yourdomain.com/connectors/connect/facebook/callback/'

# CSRF_TRUSTED_ORIGINS = ["http://127.0.0.1"]
# CORS_ALLOWED_ORIGINS = ["http://127.0.0.1"]
# TODO - open CSRF_TRUSTED_ORIGINS, CORS_ALLOWED_ORIGINS for staging and production
# CSRF_TRUSTED_ORIGINS = ["http://127.0.0.1", "https://www.salmate-staging.aibrainlab.co", "https://www.backend.salmate-staging.aibrainlab.co"]
# CORS_ALLOWED_ORIGINS = ["http://127.0.0.1", "https://www.salmate-staging.aibrainlab.co", "https://www.backend.salmate-staging.aibrainlab.co"]
# CSRF_TRUSTED_ORIGINS = os.environ.get("CSRF_TRUSTED_ORIGINS").split(',')
# CORS_ALLOWED_ORIGINS = os.environ.get("CORS_ALLOWED_ORIGINS").split(',')

# TODO - Delete this
# Add public GitHub URL for testing websocket in local
# CSRF_TRUSTED_ORIGINS = ["http://127.0.0.1", "https://www.salmate-staging.aibrainlab.co", "https://www.backend.salmate-staging.aibrainlab.co", "https://*.app.github.dev", "https://information.llm.salmate-staging.aibrainlab.co"]
# CORS_ALLOWED_ORIGINS = ["http://127.0.0.1", "https://www.salmate-staging.aibrainlab.co", "https://www.backend.salmate-staging.aibrainlab.co", "https://*.app.github.dev", "https://information.llm.salmate-staging.aibrainlab.co"]

# Add settings for restart functionality
ALLOW_AUTOMATED_RESTARTS = False  # Set to True to enable automated restarts
DEPLOYMENT_TYPE = 'docker'  # or 'systemd' depending on your setup
SERVICE_NAME = 'devproject'  # Used for systemd restart

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        # 'rest_framework.authentication.BasicAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
        # 'rest_framework.authentication.TokenAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
        # 'rest_framework_csv.renderers.CSVRenderer', # Add CSV renderer
        'drf_excel.renderers.XLSXRenderer',  # Add XLSX renderer
    ),
    # 'DEFAULT_PERMISSION_CLASSES': [
    #     'rest_framework.permissions.IsAuthenticated',
    # ],
    'TEST_REQUEST_DEFAULT_FORMAT': 'json',
    # 'DEFAULT_ORDERING': 'id',

    'DEFAULT_FILTER_BACKENDS': [
        'rest_framework.filters.OrderingFilter',
        'django_filters.rest_framework.DjangoFilterBackend',
    ],
    # Open this for pagination but the API responses when have change
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    # 'PAGE_SIZE': 10  # Number of items per page
}

LOG_PATH = os.path.join(BASE_DIR, 'logging')
if(os.path.exists(LOG_PATH) == False):
    os.makedirs(LOG_PATH)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime}:{levelname} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{asctime}:{levelname} {message}',
            'style': '{',
        },
        'broadcast': {
            'format': '\n[{asctime}] {levelname} - {name}\n{message}\n',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'api_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'api_info.log'),
            'formatter': 'verbose',
        },
        'chatbot_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'chatbot_info.log'),
            'formatter': 'verbose',
        },
        'email_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'email_info.log'),
            'formatter': 'verbose',
        },
        'ticket_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'ticket_info.log'),
            'formatter': 'verbose',
        },
        'connector_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'connector_info.log'),
            'formatter': 'verbose',
        },
        'image_buffer_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            # 'filename': 'logs/image_buffer.log',
            'filename': os.path.join(LOG_PATH, 'image_buffer.log'),
            'formatter': 'verbose',
        },
        'broadcast_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'connector_info.log'),
            'formatter': 'broadcast',
            # 'encoding': 'utf-8',
        },
        'activity_tracking_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            # 'filename': 'logs/activity_tracking.log',
            'filename': os.path.join(LOG_PATH, 'activity_tracking.log'),
            'formatter': 'verbose',
        },
        'line_rich_menu_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'line_rich_menu.log'),
            'formatter': 'verbose',
        },
        'customer_policy_crm_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_PATH, 'customer_policy_crm.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django.api_logs': {
            'handlers': ['api_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.chatbot_logs': {
            'handlers': ['chatbot_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.email_logs': {
            'handlers': ['email_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.ticket_logs': {
            'handlers': ['ticket_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.connector': {
            'handlers': ['connector_file', 'image_buffer_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'broadcast_debug': {
            'handlers': ['broadcast_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.activity_tracking': {
            'handlers': ['activity_tracking_file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.customer_policy_crm': {
            'handlers': ['customer_policy_crm_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'connectors.services.line_service.line_rich_menu_service': {
            'handlers': ['line_rich_menu_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

SWAGGER_SETTINGS = {
   'SECURITY_DEFINITIONS': {
      'Basic': {
            'type': 'basic'
      },
    #   'Bearer': {
    #         'type': 'apiKey',
    #         'name': 'Authorization',
    #         'in': 'header'
    #   }
   }
}