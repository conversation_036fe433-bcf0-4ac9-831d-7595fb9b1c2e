import logging
from typing import Dict, Any, List, Union
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated
)

from customer._services.policy_workflow_service import PolicyWorkflowService, WorkflowConfigLoader
from customer._services.policy_workflow_config_manager import get_workflow_configuration_manager

from customer.models import Customer

logger = logging.getLogger('django.api_logs')


def mask_sensitive_workflow_fields(data: Union[Dict[str, Any], List, str, Any],
                                 sensitive_fields: List[str] = None) -> Union[Dict[str, Any], List, str, Any]:
    """
    Recursively mask sensitive fields in workflow configuration data.

    Args:
        data: The data structure to process (dict, list, or primitive)
        sensitive_fields: List of field names to mask (defaults to USERNAME and PASSWORD)

    Returns:
        Data structure with sensitive fields masked
    """
    if sensitive_fields is None:
        sensitive_fields = ['username', 'password', 'USERNAME', 'PASSWORD']

    if isinstance(data, dict):
        masked_data = {}
        for key, value in data.items():
            if key.lower() in [field.lower() for field in sensitive_fields]:
                # Mask the sensitive field value
                if isinstance(value, str) and value:
                    if len(value) <= 2:
                        masked_data[key] = '*' * len(value)
                    else:
                        # Show first character and mask the rest
                        masked_data[key] = value[0] + '*' * (len(value) - 1)
                else:
                    masked_data[key] = '****'
            else:
                # Recursively process nested structures
                masked_data[key] = mask_sensitive_workflow_fields(value, sensitive_fields)
        return masked_data

    elif isinstance(data, list):
        return [mask_sensitive_workflow_fields(item, sensitive_fields) for item in data]

    else:
        # Return primitive values as-is
        return data

class CustomerPolicyListWorkflowView(APIView):
    """Execute policy list workflow and return policy list"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id, platform_id):
        """
        Execute policy list workflow for customer

        Returns:
            - policy_list_data: Raw policy list from TPA
            - member_codes: Extracted member codes
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_list_workflow(
                customer_id=customer_id,
                platform_id=platform_id,
                user=request.user
            )

            return Response(result, status=status.HTTP_200_OK)

        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy list workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerPolicyDetailsWorkflowView(APIView):
    """Execute policy details workflow for specific member code"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id, platform_id, member_code):
        """
        Execute policy details workflow for customer and member code

        Returns:
            - policy_details_data: Policy details and claims data
            - member_code: The requested member code
            - execution_metadata: Workflow execution details
        """
        try:
            result = PolicyWorkflowService.execute_policy_details_workflow(
                customer_id=customer_id,
                platform_id=platform_id,
                member_code=member_code,
                user=request.user
            )

            return Response(result, status=status.HTTP_200_OK)

        except Customer.DoesNotExist:
            return Response({
                'error': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Policy details workflow error: {str(e)}")
            return Response({
                'error': f'Workflow execution failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PolicyWorkflowConfigurationView(APIView):
    """Serve policy workflow configurations with proper data transformation"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get all available policy workflow configurations with resolved template variables and environment-specific settings

        Returns:
            - workflows: List of all available workflow configurations
            - status: Success/error status
            - legacy_format: Backward compatibility fields (policy_list_workflow, policy_details_workflow)
        """
        try:
            # Use the configuration manager to load and process workflow configurations
            config_manager = get_workflow_configuration_manager()

            # Get all available workflows from the registry
            available_workflows = WorkflowConfigLoader.list_available_workflows()
            # Process each workflow configuration
            workflows = []

            for workflow_info in available_workflows:
                workflow_id = workflow_info['id']
                try:
                    # Load and process workflow configuration
                    raw_config = config_manager.load_workflow_config(workflow_id)
                    processed_config = config_manager.prepare_frontend_config(raw_config)

                    if processed_config:
                        # Apply field masking to protect sensitive credentials
                        masked_config = mask_sensitive_workflow_fields(processed_config)
                        workflows.append(masked_config)
                        logger.info(f"PolicyWorkflowConfigurationView: Successfully loaded and masked workflow: {workflow_id}")

                except Exception as e:
                    logger.warning(f"PolicyWorkflowConfigurationView: Failed to load workflow {workflow_id}: {str(e)}")
                    continue

            # Apply masking to the entire response to ensure no sensitive data leaks
            masked_response = {
                'workflows': workflows,
                'status': 'success'
            }

            return Response(masked_response, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error loading workflow configurations: {str(e)}")
            # Ensure error messages don't contain sensitive information
            error_message = f'Failed to load workflow configurations: {str(e)}'
            # Apply basic masking to error messages in case they contain sensitive data
            masked_error = mask_sensitive_workflow_fields({'error': error_message})
            return Response(masked_error, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

