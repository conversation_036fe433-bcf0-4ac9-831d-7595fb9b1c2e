# Generated by Django 5.2.6 on 2025-09-25 06:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0009_add_policy_workflow_models'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerpolicyworkflowcache',
            name='platform_identity',
            field=models.ForeignKey(blank=True, help_text='Platform identity used for this cache entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='policy_cache', to='customer.customerplatformidentity'),
        ),
    ]
