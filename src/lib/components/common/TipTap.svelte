<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
    import { LetterBoldOutline, LetterItalicOutline, LetterUnderlineOutline, LinkOutline, ListOutline } from 'flowbite-svelte-icons';
	// @ts-ignore
	import { Editor } from '@tiptap/core';
	// @ts-ignore
	import StarterKit from '@tiptap/starter-kit';
	// @ts-ignore
	import TextAlign from '@tiptap/extension-text-align';
	// @ts-ignore
	import TextStyle from '@tiptap/extension-text-style';
	// @ts-ignore
	import Color from '@tiptap/extension-color';
	// @ts-ignore
	import Highlight from '@tiptap/extension-highlight';

    // @ts-ignore
    import Bold from '@tiptap/extension-bold'
    // @ts-ignore
    import Underline from '@tiptap/extension-underline'
    // @ts-ignore
    import Italic from '@tiptap/extension-italic'
    // @ts-ignore
    import Link from '@tiptap/extension-link'
    // @ts-ignore
    import Strike from '@tiptap/extension-strike'
    // @ts-ignore
    import Subscript from '@tiptap/extension-subscript'
    // @ts-ignore
    import Superscript from '@tiptap/extension-superscript'

	export let content: string = '<p>Start typing...</p>';

	let element: HTMLElement;
	let editor: any;

	onMount(() => {
		editor = new Editor({
			element: element,
			extensions: [
				StarterKit,
				TextAlign.configure({
					types: ['heading', 'paragraph'],
				}),
				TextStyle,
				Color,
				Highlight.configure({
					multicolor: true,
				}),
				Bold,
				Underline,
				Italic,
				Link.configure({
					openOnClick: false,
				}),
				Strike,
				Subscript,
				Superscript
			],
			content: content,
			editorProps: {
				attributes: {
					class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',
				},
			},
			onTransaction: () => {
				// force re-render so `editor.isActive` works as expected
				editor = editor;
			},
		});
	});

	onDestroy(() => {
		if (editor) {
			editor.destroy();
		}
	});

	// Helper function to toggle link
	const toggleLink = () => {
		const previousUrl = editor.getAttributes('link').href;
		const url = window.prompt('URL', previousUrl);

		// cancelled
		if (url === null) {
			return;
		}

		// empty
		if (url === '') {
			editor.chain().focus().extendMarkRange('link').unsetLink().run();
			return;
		}

		// update link
		editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
	};
</script>

<!-- Editor Container -->
<div class="tiptap-editor-container">
	<!-- Editor Content Area -->
	<div class="editor-content-area">
		<div bind:this={element} class="editor-content" />
	</div>

	<!-- Bottom Toolbar -->
	{#if editor}
		<div class="toolbar">
			<!-- Undo/Redo -->
			<button
				class="toolbar-btn"
				on:click={() => editor.chain().focus().undo().run()}
				disabled={!editor.can().undo()}
				title="Undo"
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
				</svg>
			</button>
			<button
				class="toolbar-btn"
				on:click={() => editor.chain().focus().redo().run()}
				disabled={!editor.can().redo()}
				title="Redo"
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10H11a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6"/>
				</svg>
			</button>

			<!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Font Family Dropdown -->
			<div class="toolbar-dropdown">
				<select class="font-select">
					<option>Sans Serif</option>
					<option>Serif</option>
					<option>Monospace</option>
				</select>
				<svg class="w-3 h-3 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</div>

			<!-- Text Size -->
			<div class="toolbar-dropdown">
				<select class="size-select">
					<option>TT</option>
				</select>
				<svg class="w-3 h-3 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</div>

			<!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Bold -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('bold')}
				on:click={() => editor.chain().focus().toggleBold().run()}
				title="Bold"
			>
				<LetterBoldOutline />
			</button>

			<!-- Italic -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('italic')}
				on:click={() => editor.chain().focus().toggleItalic().run()}
				title="Italic"
			>
				<LetterItalicOutline />
			</button>

			<!-- Underline -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('underline')}
				on:click={() => editor.chain().focus().toggleUnderline().run()}
				title="Underline"
			>
				<LetterUnderlineOutline />
			</button>

			<!-- Link -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('link')}
				on:click={toggleLink}
				title="Link"
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
				</svg>
			</button>

			<!-- Text Color -->
			<button class="toolbar-btn" title="Text Color">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h16M7 21l4-4M7 21l-4-4"/>
				</svg>
				<svg class="w-3 h-3 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</button>

			<!-- Separator -->
			<div class="toolbar-separator"></div>

			<!-- Align Left -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive({ textAlign: 'left' })}
				on:click={() => editor.chain().focus().setTextAlign('left').run()}
				title="Align Left"
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"/>
				</svg>
			</button>

			<!-- Bullet List -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('bulletList')}
				on:click={() => editor.chain().focus().toggleBulletList().run()}
				title="Bullet List"
			>
				<!-- <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
				</svg> -->
                <ListOutline />
			</button>

			<!-- Numbered List -->
			<button
				class="toolbar-btn"
				class:active={editor.isActive('orderedList')}
				on:click={() => editor.chain().focus().toggleOrderedList().run()}
				title="Numbered List"
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
				</svg>
			</button>

			<!-- Indent -->
			<!-- <button class="toolbar-btn" title="Increase Indent">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5"/>
				</svg>
			</button> -->

			<!-- Outdent -->
			<!-- <button class="toolbar-btn" title="Decrease Indent">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5"/>
				</svg>
			</button> -->

			<!-- More Options -->
			<!-- <button class="toolbar-btn" title="More Options">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
				</svg>
			</button> -->
		</div>
	{/if}
</div>

<style>
	.tiptap-editor-container {
		border: 1px solid #d1d5db;
		border-radius: 8px;
		background-color: white;
		box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
		min-height: 300px;
		display: flex;
		flex-direction: column;
	}

	.editor-content-area {
		flex: 1;
		background-color: white;
		border-radius: 8px 8px 0 0;
	}

	.editor-content {
		width: 100%;
		height: 100%;
		min-height: 200px;
	}

	/* Tiptap editor content styling */
	:global(.tiptap-editor-container .ProseMirror) {
		outline: none;
		border: none;
		padding: 16px;
		min-height: 200px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		font-size: 14px;
		line-height: 1.5;
		color: #374151;
	}

	:global(.tiptap-editor-container .ProseMirror p) {
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror h1) {
		font-size: 1.5rem;
		font-weight: bold;
		margin-bottom: 16px;
	}

	:global(.tiptap-editor-container .ProseMirror h2) {
		font-size: 1.25rem;
		font-weight: bold;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror ul) {
		list-style-type: disc;
		padding-left: 24px;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror ol) {
		list-style-type: decimal;
		padding-left: 24px;
		margin-bottom: 12px;
	}

	:global(.tiptap-editor-container .ProseMirror li) {
		margin-bottom: 4px;
	}

	:global(.tiptap-editor-container .ProseMirror strong) {
		font-weight: bold;
	}

	:global(.tiptap-editor-container .ProseMirror em) {
		font-style: italic;
	}

	:global(.tiptap-editor-container .ProseMirror u) {
		text-decoration: underline;
	}

	:global(.tiptap-editor-container .ProseMirror a) {
		color: #2563eb;
		text-decoration: underline;
	}

	/* Toolbar styling */
	.toolbar {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 8px 12px;
		background-color: #f9fafb;
		border-top: 1px solid #e5e7eb;
		border-radius: 0 0 8px 8px;
		min-height: 44px;
	}

	.toolbar-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 32px;
		height: 32px;
		border-radius: 4px;
		border: none;
		background: transparent;
		color: #374151;
		font-size: 14px;
		cursor: pointer;
		transition: background-color 0.15s ease-in-out;
	}

	.toolbar-btn:hover {
		background-color: #e5e7eb;
	}

	.toolbar-btn:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.toolbar-btn.active {
		background-color: #e5e7eb;
		color: #111827;
	}

	.toolbar-separator {
		width: 1px;
		height: 24px;
		background-color: #d1d5db;
		margin: 0 4px;
	}

	.toolbar-dropdown {
		position: relative;
		display: flex;
		align-items: center;
	}

	.font-select,
	.size-select {
		appearance: none;
		background: transparent;
		border: none;
		outline: none;
		padding-right: 20px;
		padding-left: 8px;
		padding-top: 4px;
		padding-bottom: 4px;
		font-size: 14px;
		cursor: pointer;
		color: #374151;
		min-width: 80px;
	}

	.size-select {
		min-width: 40px;
		text-align: center;
	}

	.dropdown-arrow {
		position: absolute;
		right: 4px;
		pointer-events: none;
		color: #6b7280;
	}

	/* Responsive adjustments */
	@media (max-width: 640px) {
		.toolbar {
			flex-wrap: wrap;
			gap: 4px;
			padding: 8px;
		}

		.toolbar-btn {
			width: 28px;
			height: 28px;
		}

		.font-select {
			min-width: 60px;
		}
	}
</style>