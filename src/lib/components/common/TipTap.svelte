<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	// @ts-ignore
	import { Editor } from '@tiptap/core';
	// @ts-ignore
	import StarterKit from '@tiptap/starter-kit';

    // @ts-ignore
    import Bold from '@tiptap/extension-bold'
    // @ts-ignore
    import Underline from '@tiptap/extension-underline'
    // @ts-ignore
    import Italic from '@tiptap/extension-italic'
    // @ts-ignore
    import Link from '@tiptap/extension-link'
    // @ts-ignore
    import Strike from '@tiptap/extension-strike'
    // @ts-ignore
    import Subscript from '@tiptap/extension-subscript'
    // @ts-ignore
    import Superscript from '@tiptap/extension-superscript'


	let element: HTMLElement;
	let editor: any;

	onMount(() => {
		editor = new Editor({
			element: element,
			extensions: [StarterKit, Bold, Underline, Italic, Link, Strike, Subscript, Superscript],
			content: '<p>Hello World! 🌍️ </p>',
			onTransaction: () => {
				// force re-render so `editor.isActive` works as expected
				editor = editor;
			},
		});
	});

	onDestroy(() => {
		if (editor) {
			editor.destroy();
		}
	});
</script>

{#if editor}
	<button
		on:click={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
		class:active={editor.isActive('heading', { level: 1 })}
	>
		H1
	</button>
	<button
		on:click={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
		class:active={editor.isActive('heading', { level: 2 })}
	>
		H2
	</button>
	<button
		on:click={() => editor.chain().focus().setParagraph().run()}
		class:active={editor.isActive('paragraph')}
	>
		P
	</button>
    <button
		on:click={() => editor.chain().focus().toggleBold().run()}
		class:active={editor.isActive('bold')}
	>
		Bold
	</button>
    <button
		on:click={() => editor.chain().focus().toggleUnderline().run()}
		class:active={editor.isActive('underline')}
	>
		Underline
	</button>
    <button
		on:click={() => editor.chain().focus().toggleItalic().run()}
		class:active={editor.isActive('italic')}
	>
		Italic
	</button>
    <button
		on:click={() => {
			const url = window.prompt('URL');
			if (url) {
				editor.chain().focus().toggleLink({ href: url }).run();
			}
		}}
		class:active={editor.isActive('link')}
	>
		Link
	</button>
    <button
		on:click={() => editor.chain().focus().toggleStrike().run()}
		class:active={editor.isActive('strike')}
	>
		Strike
	</button>
    <button
		on:click={() => editor.chain().focus().toggleSubscript().run()}
		class:active={editor.isActive('subscript')}
	>
		Subscript
	</button>
    <button
		on:click={() => editor.chain().focus().toggleSuperscript().run()}
		class:active={editor.isActive('superscript')}
	>
		Superscript
	</button>
{/if}

<div bind:this={element} />

<style>
	button.active {
		background: black;
		color: white;
	}
</style>