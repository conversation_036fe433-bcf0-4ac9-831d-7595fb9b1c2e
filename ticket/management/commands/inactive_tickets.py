import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import timedel<PERSON>

from linechatbot.tasks import send_message_via_route_message_to_customer
from customer.tasks import broadcast_platform_message_update
from setting.services import SettingsService
from ticket.models import Ticket, Message, Status, StatusLog
from ticket.services.ticket_service import TicketService
from ticket.utils import update_ticket_status, analyze_ticket_from_api
from user.models import User

logger = logging.getLogger('django.ticket')


class Command(BaseCommand):
    """
    Send prompt messages to inactive tickets and close them after timeout.
    
    Workflow:
    1. Check if open tickets are inactive for INACTIVE_TICKET_2ND_TIME_MINUTES
       -> Send closing message and close them
    2. Check if open tickets are inactive for INACTIVE_TICKET_1ST_TIME_MINUTES
       -> Send prompt message asking if they need help (only once)
    3. Check if pending_to_close tickets are waiting for CSAT response
       -> Close them after timeout
    """
    help = "Send prompt messages to inactive tickets and close them after some minutes"

    def __init__(self):
        super().__init__()
        self.system_user = None
        self.closed_status = None
        self.settings = {}
        
    def handle(self, *args, **kwargs):
        """Main command handler."""
        self.stdout.write(self.style.WARNING('Task Schedule: Check and Close inactive tickets'))
        logger.info(f"inactive_tickets's handle - Task Schedule: Check and Close inactive tickets")
        
        try:
            # Initialize common objects
            self._initialize_common_objects()
            logger.info(f"inactive_tickets's handle's _initialize_common_objects() is executed")
            
            # Process open tickets
            self._process_open_tickets()
            logger.info(f"inactive_tickets's handle's _process_open_tickets() is executed")
            
            # Process pending_to_close tickets
            self._process_pending_to_close_tickets()
            logger.info(f"inactive_tickets's handle's _process_pending_to_close_tickets() is executed")
            
        except Exception as e:
            logger.error(f"Error in inactive tickets command: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
    
    def _initialize_common_objects(self):
        """Initialize commonly used objects and settings."""
        self.system_user = User.objects.get(name="System")
        self.closed_status = Status.objects.get(name="closed")
        
        # Load settings
        self.settings = {
            'INACTIVE_TICKET_1ST_TIME_MINUTES': int(
                SettingsService.get_setting("INACTIVE_TICKET_1ST_TIME_MINUTES", default=30)
            ),
            'INACTIVE_TICKET_2ND_TIME_MINUTES': int(
                SettingsService.get_setting("INACTIVE_TICKET_2ND_TIME_MINUTES", default=60)
            ),
            'INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES': int(
                SettingsService.get_setting("INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES", default=10)
            )
        }

        # TODO - Delete this or Log this
        print(f"inactive_tickets' settings' settings = {self.settings} ")
        
        logger.info(f"Inactive ticket settings: {self.settings}")
    
    def _process_open_tickets(self):
        """Process all open tickets for inactivity."""
        open_tickets = TicketService.get_tickets_by_status('open')
        
        self.stdout.write(f"Found {open_tickets.count()} open tickets to check")
        
        for ticket in open_tickets:
            try:
                self._check_ticket_inactivity(ticket)
            except Exception as e:
                logger.error(f"Error processing ticket {ticket.id}: {str(e)}")
                continue
    
    def _check_ticket_inactivity(self, ticket: Ticket):
        """Check and handle individual ticket inactivity."""
        # Get latest messages
        latest_messages = self._get_latest_messages(ticket)
        if not latest_messages['customer']:
            logger.warning(f"Ticket {ticket.id} has no customer messages")
            return
        
        # Calculate inactivity time
        diff_customer_time = timezone.now() - latest_messages['customer'].created_on
        inactivity_minutes = diff_customer_time.total_seconds() / 60
        
        logger.debug(f"Ticket {ticket.id} inactive for {inactivity_minutes:.1f} minutes")
        
        # Check for second timeout (close ticket)
        if inactivity_minutes >= self.settings['INACTIVE_TICKET_2ND_TIME_MINUTES']:
            self._close_inactive_ticket(ticket)
        
        # Check for first timeout (send prompt)
        elif inactivity_minutes >= self.settings['INACTIVE_TICKET_1ST_TIME_MINUTES']:
            self._send_inactivity_prompt(ticket, latest_messages.get('user'))
    
    def _get_latest_messages(self, ticket: Ticket) -> dict:
        """Get latest user and customer messages for a ticket."""
        messages = {
            'user': None,
            'customer': None
        }
        
        try:
            # Latest message from user (agent)
            messages['user'] = Message.objects.filter(
                ticket_id=ticket.id,
                is_self=True
            ).latest('id')
        except Message.DoesNotExist:
            pass
        
        try:
            # Latest message from customer
            messages['customer'] = Message.objects.filter(
                ticket_id=ticket.id,
                is_self=False
            ).latest('id')
        except Message.DoesNotExist:
            pass
        
        return messages
    
    @transaction.atomic
    def _close_inactive_ticket(self, ticket: Ticket):
        """Close ticket due to inactivity."""
        try:
            # Analyze ticket before closing
            analyze_ticket_from_api(
                ticket_id=ticket.id, 
                action_by_user=self.system_user, 
                created_by_user=self.system_user, 
                action="close-ticket"
            )
            
            # Update ticket status
            update_ticket_status(ticket, self.closed_status, ticket.owner_id)
            
            self.stdout.write(
                self.style.WARNING(f'Ticket {ticket.id} closed due to inactivity.')
            )
            
        except Exception as e:
            logger.error(f"Error closing ticket {ticket.id}: {str(e)}")
    
    @transaction.atomic
    def _send_inactivity_prompt(self, ticket: Ticket, latest_user_message: Message):
        """Send inactivity prompt message if not already sent."""
        prompt_message_text = """คุณลูกค้าได้รับข้อมูลครบถ้วนหรือไม่ หรือ มีอะไรสอบถามเพิ่มเติมไหม (ถ้าไม่มี คุณลูกค้าไม่ต้องพิมพ์อะไรเพิ่มเติม)

Did you receive complete information? Or do you have any additional questions? (If you do not wish to ask any additional questions, you do not need to answer this question.)"""
        prompt_message_text = """คุณลูกค้าได้รับข้อมูลครบถ้วนหรือไม่ หรือ มีอะไรสอบถามเพิ่มเติมไหม (ถ้าไม่มี คุณลูกค้าไม่ต้องพิมพ์อะไรเพิ่มเติม)"""
        
        # Check if prompt was already sent
        if latest_user_message and latest_user_message.message == prompt_message_text:
            logger.debug(f"Ticket {ticket.id}: Prompt already sent, skipping")
            return
        
        # Create prompt message
        prompt_message = Message.objects.create(
            ticket_id=ticket,
            message=prompt_message_text,
            user_name=ticket.owner_id.name,
            is_self=True,
            platform_identity=ticket.platform_identity,
            created_by=self.system_user
        )
        
        # Send message to customer
        send_message_via_route_message_to_customer.delay(
            ticket_id=ticket.id,
            message_content=prompt_message.message,
            message_type='TEXT',
            event_reply_token=None,
            bool_create_outgoing_message=False
        )
        
        # # Broadcast update
        # if ticket.platform_identity:
        #     broadcast_platform_message_update.delay(
        #         ticket.platform_identity.id, 
        #         prompt_message.id
        #     )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Sent prompt message to Customer {ticket.customer_id.name} '
                f'on Ticket {ticket.id} (inactive for '
                f'{self.settings["INACTIVE_TICKET_1ST_TIME_MINUTES"]} minutes)'
            )
        )
    
    def _process_pending_to_close_tickets(self):
        """Process tickets in pending_to_close status."""
        pending_tickets = TicketService.get_tickets_by_status('pending_to_close')
        
        self.stdout.write(f"Found {pending_tickets.count()} pending_to_close tickets")
        
        for ticket in pending_tickets:
            try:
                self._check_pending_ticket_timeout(ticket)
            except Exception as e:
                logger.error(f"Error processing pending ticket {ticket.id}: {str(e)}")
                continue
    
    @transaction.atomic
    def _check_pending_ticket_timeout(self, ticket: Ticket):
        """Check if pending_to_close ticket has timed out waiting for CSAT."""
        try:
            # Get the latest status log for pending_to_close
            pending_status_log = StatusLog.objects.filter(
                ticket_id=ticket.id,
                status_id__name='pending_to_close'
            ).latest('created_on')
            
            # Calculate time since status change
            diff_status_time = timezone.now() - pending_status_log.created_on
            timeout_minutes = diff_status_time.total_seconds() / 60
            
            logger.debug(
                f"Ticket {ticket.id} in pending_to_close for {timeout_minutes:.1f} minutes"
            )
            
            # Check if timeout exceeded
            if timeout_minutes >= self.settings['INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES']:
                self._close_pending_ticket(ticket)
                
        except StatusLog.DoesNotExist:
            logger.warning(f"No pending_to_close status log found for ticket {ticket.id}")
    
    @transaction.atomic
    def _close_pending_ticket(self, ticket: Ticket):
        """Close pending ticket after CSAT timeout."""
        # Create closing message
        closing_message_text = """เนื่องจากไม่ได้รับข้อมูลเพิ่มเติม ขออนุญาตจบการให้บริการในครั้งนี้ก่อน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง

Since we have not received any additional information, we would like to end your ticket for now. If you would like to inquire any information, you can contact us 24 hours a day."""
        closing_message_text = """เนื่องจากไม่ได้รับข้อมูลเพิ่มเติม ขออนุญาตจบการให้บริการในครั้งนี้ก่อน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง"""
        
        closing_message = Message.objects.create(
            ticket_id=ticket,
            message=closing_message_text,
            user_name=ticket.owner_id.name,
            is_self=True,
            platform_identity=ticket.platform_identity,
            created_by=self.system_user
        )
        
        # Send message to customer
        send_message_via_route_message_to_customer.delay(
            ticket_id=ticket.id,
            message_content=closing_message.message,
            message_type='TEXT',
            event_reply_token=None,
            bool_create_outgoing_message=False
        )
        
        # # Broadcast update
        # if ticket.platform_identity:
        #     broadcast_platform_message_update.delay(
        #         ticket.platform_identity.id,
        #         closing_message.id
        #     )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Sent closing message to Customer {ticket.customer_id.name} '
                f'on Ticket {ticket.id} (no CSAT response for '
                f'{self.settings["INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES"]} minutes)'
            )
        )
        
        # Close the ticket
        update_ticket_status(ticket, self.closed_status, ticket.owner_id)
        
        self.stdout.write(
            self.style.WARNING(
                f'Ticket {ticket.id} closed - customer did not provide feedback'
            )
        )